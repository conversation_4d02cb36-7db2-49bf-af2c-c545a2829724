"use client";

import React, { useState } from "react";
import { Button } from "@heroui/react";
import { useRouter } from "next/navigation";

import { title } from "@/components/primitives";
import ReportStepper from "@/components/report/create/report-stepper";
import SelectableFieldsTable from "@/components/report/create/selectable-fields-table";
import ReportFiltersStep from "@/components/report/create/report-filters-step";
import ReportSummary from "@/components/report/create/report-summary";

// Field filter options for each field type
export interface FieldFilter {
  fieldId: string;
  fieldName: string;
  fieldType: string;
  filterOption: "all" | "with_content" | "without_content";
}

function ReportContent() {
  const router = useRouter();

  const [currentStep, setCurrentStep] = useState(1);
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [fieldFilters, setFieldFilters] = useState<FieldFilter[]>([]);

  const steps = [
    {
      key: 1,
      title: "Sele<PERSON><PERSON>r campos",
      description: "Elige los campos para el reporte",
    },
    {
      key: 2,
      title: "Configurar filtros",
      description: "Define los filtros para cada campo",
    },
    {
      key: 3,
      title: "Confirmar creación",
      description: "Revisa y confirma el reporte",
    },
  ];

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (step: number) => {
    setCurrentStep(step);
  };

  const handleCreateReport = async () => {
    // TODO: Implement report creation logic
    console.log(
      JSON.stringify(
        {
          selectedFields,
          fieldFilters,
        },
        null,
        2,
      ),
    );

    router.push("/configuracion");
  };

  const currentStepData = steps.find((step) => step.key === currentStep);
  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === 3;

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <SelectableFieldsTable
            selectedFields={selectedFields}
            onSelectionChange={setSelectedFields}
          />
        );
      case 2:
        return (
          <ReportFiltersStep
            selectedFields={selectedFields}
            fieldFilters={fieldFilters}
            onFiltersChange={setFieldFilters}
          />
        );
      case 3:
        return (
          <ReportSummary
            selectedFields={selectedFields}
            fieldFilters={fieldFilters}
            onCreateReport={handleCreateReport}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className="flex justify-between items-center w-full pb-4">
        <div>
          <h2 className={title({ size: "sm" })}>
            Paso {currentStep}: {currentStepData?.title}
          </h2>
          <p className="text-default-500 mt-1">
            {currentStepData?.description}
          </p>
        </div>
        <div className="flex gap-2">
          {!isFirstStep && (
            <Button variant="bordered" onPress={handlePrevious}>
              Anterior
            </Button>
          )}
          {!isLastStep && (
            <Button color="primary" onPress={handleNext}>
              Siguiente
            </Button>
          )}
        </div>
      </div>

      <ReportStepper
        currentStep={currentStep}
        steps={steps}
        onStepClick={handleStepClick}
      />

      {renderStepContent()}
    </>
  );
}

export default function CrearReportePage() {
  return (
    <div className="flex flex-col gap-4 p-4">
      <ReportContent />
    </div>
  );
}
