import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>ooter,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Tooltip,
  Chip,
} from "@heroui/react";
import { Icon } from "@iconify/react";

export interface Task {
  id: string;
  name: string;
  description: string;
  status: "En progreso" | "Ni empezada";
  dueDate: string;
}

export interface TasksModalProps {
  isOpen: boolean;
  onClose: () => void;
  tasks: Task[];
  onViewTask: (taskId: string) => void;
}

const getStatusColor = (status: Task["status"]) => {
  switch (status) {
    case "En progreso":
      return "warning";
    case "Ni empezada":
      return "danger";
    default:
      return "default";
  }
};

export const TasksModal = ({
  isOpen,
  onClose,
  tasks,
  onViewTask,
}: TasksModalProps) => {
  return (
    <Modal isOpen={isOpen} scrollBehavior="inside" size="4xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-xl font-bold">Lista de Tareas</h2>
              <p className="text-small text-default-500">
                Gestiona tus tareas de manera eficiente
              </p>
            </ModalHeader>
            <ModalBody>
              <Table
                removeWrapper
                aria-label="Tabla de tareas"
                classNames={{
                  th: "bg-default-100 text-default-800",
                  td: "py-3",
                }}
              >
                <TableHeader>
                  <TableColumn>TAREA</TableColumn>
                  <TableColumn>DESCRIPCIÓN</TableColumn>
                  <TableColumn>ESTADO</TableColumn>
                  <TableColumn>FECHA PREVISTA</TableColumn>
                  <TableColumn>ACCIÓN</TableColumn>
                </TableHeader>
                <TableBody emptyContent="No hay tareas disponibles">
                  {tasks.map((task) => (
                    <TableRow key={task.id}>
                      <TableCell>{task.name}</TableCell>
                      <TableCell>
                        <Tooltip content={task.description}>
                          <span className="cursor-help truncate max-w-xs inline-block">
                            {task.description}
                          </span>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Chip
                          color={getStatusColor(task.status)}
                          size="sm"
                          variant="flat"
                        >
                          {task.status}
                        </Chip>
                      </TableCell>
                      <TableCell>
                        <Chip
                          color={
                            new Date(task.dueDate) < new Date()
                              ? "danger"
                              : "success"
                          }
                          variant="dot"
                        >
                          {task.dueDate}
                        </Chip>
                      </TableCell>
                      <TableCell>
                        <Button
                          color="primary"
                          endContent={<Icon icon="lucide:arrow-right" />}
                          variant="flat"
                          onPress={() => onViewTask(task.id)}
                        >
                          Ver tarea
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Cerrar
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
