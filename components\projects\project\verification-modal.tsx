import React from "react";
import { Icon } from "@iconify/react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>ooter,
  ModalBody,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Progress,
} from "@heroui/react";

export interface VerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Phase {
  name: string;
  completed: boolean;
  verified: boolean;
  percentage: number;
}

export const VerificationModal: React.FC<VerificationModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [phases, setPhases] = React.useState<Phase[]>([
    { name: "Inicio", completed: true, verified: true, percentage: 100 },
    { name: "Recolección", completed: true, verified: false, percentage: 75 },
    { name: "Mi<PERSON><PERSON>", completed: false, verified: false, percentage: 50 },
    { name: "<PERSON>rue<PERSON>", completed: false, verified: false, percentage: 25 },
    { name: "Lanzamiento", completed: false, verified: false, percentage: 0 },
    { name: "<PERSON><PERSON><PERSON>", completed: false, verified: false, percentage: 0 },
  ]);

  const handleVerify = (index: number) => {
    setPhases((prevPhases) =>
      prevPhases.map((phase, i) =>
        i === index ? { ...phase, verified: !phase.verified } : phase,
      ),
    );
  };

  return (
    <Modal isOpen={isOpen} size="3xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              Verificación de Fases
            </ModalHeader>
            <ModalBody>
              <Table removeWrapper aria-label="Tabla de verificación de fases">
                <TableHeader>
                  <TableColumn>FASE</TableColumn>
                  <TableColumn>ESTADO</TableColumn>
                  <TableColumn>VERIFICADO</TableColumn>
                  <TableColumn>PROGRESO</TableColumn>
                  <TableColumn>ACCIÓN</TableColumn>
                </TableHeader>
                <TableBody>
                  {phases.map((phase, index) => (
                    <TableRow key={phase.name}>
                      <TableCell>{phase.name}</TableCell>
                      <TableCell>
                        <span
                          className={`flex items-center gap-1 ${
                            phase.percentage === 0
                              ? "text-danger"
                              : phase.completed
                                ? "text-success"
                                : "text-warning"
                          }`}
                        >
                          <Icon
                            icon={
                              phase.percentage === 0
                                ? "lucide:x-circle"
                                : phase.completed
                                  ? "lucide:check-circle"
                                  : "lucide:clock"
                            }
                          />
                          {phase.percentage === 0
                            ? "No Iniciado"
                            : phase.completed
                              ? "Completado"
                              : "En Progreso"}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={`flex items-center gap-1 ${phase.verified ? "text-success" : "text-danger"}`}
                        >
                          <Icon
                            icon={phase.verified ? "lucide:check" : "lucide:x"}
                          />
                          {phase.verified ? "Verificado" : "No Verificado"}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            className="max-w-md"
                            color={
                              phase.percentage === 100 ? "success" : "primary"
                            }
                            size="sm"
                            value={phase.percentage}
                          />
                          <span className="text-sm">{phase.percentage}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          className="w-full"
                          color={phase.verified ? "default" : "primary"}
                          size="sm"
                          onPress={() => handleVerify(index)}
                        >
                          {phase.verified ? "Desverificar" : "Verificar"}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Cerrar
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
