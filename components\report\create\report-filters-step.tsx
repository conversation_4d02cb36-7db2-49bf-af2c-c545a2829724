"use client";

import React, { useEffect } from "react";
import {
  Card,
  CardBody,
  CardHeader,
  Select,
  SelectItem,
  Chip,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useAllFieldsQuery } from "@/graphql/schemas/generated";
import { FieldFilter } from "@/app/configuracion/reportes/crear/page";

interface ReportFiltersStepProps {
  selectedFields: string[];
  fieldFilters: FieldFilter[];
  onFiltersChange: (filters: FieldFilter[]) => void;
}

// Filter options for each field type
const getFilterOptionsForType = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return [
        { key: "all", label: "Todos los campos" },
        { key: "with_content", label: "Solo con contenido" },
        { key: "without_content", label: "Solo sin contenido" },
      ];
    case "selection":
      return [
        { key: "all", label: "Todas las selecciones" },
        { key: "with_content", label: "Solo con selección" },
        { key: "without_content", label: "Solo sin selección" },
      ];
    case "task":
      return [
        { key: "all", label: "Todas las tareas" },
        { key: "with_content", label: "Solo completadas" },
        { key: "without_content", label: "Solo pendientes" },
      ];
    case "document":
      return [
        { key: "all", label: "Todos los documentos" },
        { key: "with_content", label: "Solo con documento" },
        { key: "without_content", label: "Solo sin documento" },
      ];
    case "task_with_subtasks":
      return [
        { key: "all", label: "Todas las subtareas" },
        { key: "with_content", label: "Solo completadas" },
        { key: "without_content", label: "Solo pendientes" },
      ];
    default:
      return [
        { key: "all", label: "Todos" },
        { key: "with_content", label: "Con contenido" },
        { key: "without_content", label: "Sin contenido" },
      ];
  }
};

const getFieldTypeDisplayName = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return "Informativo";
    case "selection":
      return "Selección";
    case "task":
      return "Tarea";
    case "document":
      return "Documento";
    case "task_with_subtasks":
      return "Subtarea";
    default:
      return fieldType;
  }
};

const getFieldTypeIcon = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return "heroicons:information-circle";
    case "selection":
      return "heroicons:list-bullet";
    case "task":
      return "heroicons:check-circle";
    case "document":
      return "heroicons:document";
    case "task_with_subtasks":
      return "heroicons:squares-plus";
    default:
      return "heroicons:question-mark-circle";
  }
};

export default function ReportFiltersStep({
  selectedFields,
  fieldFilters,
  onFiltersChange,
}: ReportFiltersStepProps) {
  const { data: fieldsData } = useAllFieldsQuery();

  const fields = fieldsData?.allFields || [];

  // Get selected field details
  const selectedFieldsData = fields.filter((field) =>
    selectedFields.includes(field?.id || "")
  );

  // Initialize filters for selected fields
  useEffect(() => {
    const newFilters: FieldFilter[] = selectedFieldsData.map((field) => {
      const existingFilter = fieldFilters.find(
        (filter) => filter.fieldId === field?.id
      );

      return (
        existingFilter || {
          fieldId: field?.id || "",
          fieldName: field?.name || "",
          fieldType: field?.type || "",
          filterOption: "all" as const,
        }
      );
    });

    onFiltersChange(newFilters);
  }, [selectedFields, selectedFieldsData]);

  const handleFilterChange = (
    fieldId: string,
    filterOption: "all" | "with_content" | "without_content"
  ) => {
    const updatedFilters = fieldFilters.map((filter) =>
      filter.fieldId === fieldId ? { ...filter, filterOption } : filter
    );

    onFiltersChange(updatedFilters);
  };

  // Group fields by type
  const fieldsByType = selectedFieldsData.reduce((acc, field) => {
    const type = field?.type || "unknown";
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(field);
    return acc;
  }, {} as Record<string, any[]>);

  if (selectedFields.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <Icon
          icon="heroicons:exclamation-triangle"
          width={48}
          className="text-warning mb-4"
        />
        <h3 className="text-lg font-semibold mb-2">No hay campos seleccionados</h3>
        <p className="text-default-500">
          Regresa al paso anterior para seleccionar los campos que deseas incluir en el reporte.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold mb-2">Configurar filtros por tipo de campo</h3>
        <p className="text-default-500">
          Define cómo filtrar los datos para cada tipo de campo seleccionado
        </p>
      </div>

      {Object.entries(fieldsByType).map(([fieldType, fieldsOfType]) => (
        <Card key={fieldType} className="w-full">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-3">
              <Icon
                icon={getFieldTypeIcon(fieldType)}
                width={24}
                className="text-primary"
              />
              <div>
                <h4 className="text-lg font-semibold">
                  {getFieldTypeDisplayName(fieldType)}
                </h4>
                <p className="text-sm text-default-500">
                  {fieldsOfType.length} campo(s) de este tipo
                </p>
              </div>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              {fieldsOfType.map((field) => {
                const fieldFilter = fieldFilters.find(
                  (filter) => filter.fieldId === field?.id
                );
                const filterOptions = getFilterOptionsForType(fieldType);

                return (
                  <div
                    key={field?.id}
                    className="flex items-center justify-between p-4 bg-default-50 rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h5 className="font-medium">{field?.name}</h5>
                        <Chip size="sm" variant="flat">
                          {field?.subphase?.phase?.name} - {field?.subphase?.name}
                        </Chip>
                      </div>
                      {field?.description && (
                        <p className="text-sm text-default-600">
                          {field.description}
                        </p>
                      )}
                    </div>
                    <div className="ml-4 min-w-[200px]">
                      <Select
                        size="sm"
                        placeholder="Seleccionar filtro"
                        selectedKeys={
                          fieldFilter?.filterOption
                            ? [fieldFilter.filterOption]
                            : []
                        }
                        onSelectionChange={(keys) => {
                          const selectedKey = Array.from(keys)[0] as
                            | "all"
                            | "with_content"
                            | "without_content";
                          if (selectedKey) {
                            handleFilterChange(field?.id || "", selectedKey);
                          }
                        }}
                      >
                        {filterOptions.map((option) => (
                          <SelectItem key={option.key} value={option.key}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </Select>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardBody>
        </Card>
      ))}
    </div>
  );
}
